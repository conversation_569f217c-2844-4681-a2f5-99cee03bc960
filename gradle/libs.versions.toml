[versions]
# libraries
junit = "4.13.2"
javimmutable-collections = "2.4.1"
assertj = "3.27.3"
mockito = "5.18.0"

# plugins
changelog = "2.2.1"
intelliJPlatform = "2.6.0"
kotlin = "2.1.21"
kover = "0.9.1"
qodana = "2025.1.1"

[libraries]
junit = { group = "junit", name = "junit", version.ref = "junit" }
javimmutableCollections = { group = "org.javimmutable", name = "javimmutable-collections", version.ref = "javimmutable-collections" }
assertj = { group = "org.assertj", name = "assertj-core", version.ref = "assertj" }
mockito =  { group = "org.mockito", name = "mockito-core", version.ref = "mockito" }

[plugins]
changelog = { id = "org.jetbrains.changelog", version.ref = "changelog" }
intelliJPlatform = { id = "org.jetbrains.intellij.platform", version.ref = "intelliJPlatform" }
kotlin = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kover = { id = "org.jetbrains.kotlinx.kover", version.ref = "kover" }
qodana = { id = "org.jetbrains.qodana", version.ref = "qodana" }
