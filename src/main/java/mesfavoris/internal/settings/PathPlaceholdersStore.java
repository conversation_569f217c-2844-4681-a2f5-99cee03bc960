package mesfavoris.internal.settings;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.PersistentStateComponent;
import com.intellij.openapi.components.State;
import com.intellij.openapi.components.Storage;
import mesfavoris.placeholders.IPathPlaceholders;
import mesfavoris.placeholders.PathPlaceholder;
import org.jdom.Element;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Persistent store for path placeholders
 */
@State(
    name = "PathPlaceholdersStore",
    storages = @Storage("path-placeholders.xml")
)
public class PathPlaceholdersStore implements PersistentStateComponent<PathPlaceholdersStore.State>, IPathPlaceholders {

    public static class State {
        public List<PlaceholderData> placeholders = new ArrayList<>();

        public State() {
        }
    }

    public static class PlaceholderData {
        public String name = "";
        public String path = "";

        public PlaceholderData() {
        }

        public PlaceholderData(String name, String path) {
            this.name = name;
            this.path = path;
        }

        public PathPlaceholder toPathPlaceholder() {
            if (name == null || name.trim().isEmpty() || path == null || path.trim().isEmpty()) {
                return null;
            }
            try {
                return new PathPlaceholder(name.trim(), Paths.get(path.trim()));
            } catch (Exception e) {
                return null;
            }
        }

        public static PlaceholderData fromPathPlaceholder(PathPlaceholder placeholder) {
            return new PlaceholderData(placeholder.getName(), placeholder.getPath().toString());
        }
    }

    private State state = new State();

    public static PathPlaceholdersStore getInstance() {
        return ApplicationManager.getApplication().getService(PathPlaceholdersStore.class);
    }

    @Override
    public @Nullable State getState() {
        return state;
    }

    @Override
    public void loadState(@NotNull State state) {
        this.state = state;
    }

    public List<PlaceholderData> getPlaceholders() {
        return new ArrayList<>(state.placeholders);
    }

    public void setPlaceholders(List<PlaceholderData> placeholders) {
        state.placeholders = new ArrayList<>(placeholders);
    }

    public List<PathPlaceholder> getPathPlaceholders() {
        List<PathPlaceholder> result = new ArrayList<>();
        for (PlaceholderData data : state.placeholders) {
            PathPlaceholder placeholder = data.toPathPlaceholder();
            if (placeholder != null) {
                result.add(placeholder);
            }
        }
        return result;
    }

    public void setPathPlaceholders(List<PathPlaceholder> placeholders) {
        List<PlaceholderData> data = new ArrayList<>();
        for (PathPlaceholder placeholder : placeholders) {
            data.add(PlaceholderData.fromPathPlaceholder(placeholder));
        }
        state.placeholders = data;
    }

    // Implementation of IPathPlaceholders interface

    @Override
    public Iterator<PathPlaceholder> iterator() {
        return getPathPlaceholders().iterator();
    }

    @Override
    public PathPlaceholder get(String name) {
        if (name == null) {
            return null;
        }
        for (PathPlaceholder placeholder : getPathPlaceholders()) {
            if (name.equals(placeholder.getName())) {
                return placeholder;
            }
        }
        return null;
    }
}
