package mesfavoris.internal.settings;

import com.intellij.util.ui.ColumnInfo;
import com.intellij.util.ui.ListTableModel;
import mesfavoris.internal.settings.PathPlaceholdersStore.PlaceholderData;
import org.jetbrains.annotations.Nullable;

import javax.swing.table.TableCellEditor;
import javax.swing.table.TableCellRenderer;
import java.util.List;

/**
 * Table model to manage placeholders
 */
public class PlaceholdersTableModel extends ListTableModel<PlaceholderData> {

    private static final ColumnInfo<PlaceholderData, String> NAME_COLUMN = new ColumnInfo<PlaceholderData, String>("Name") {
        @Override
        public @Nullable String valueOf(PlaceholderData item) {
            return item.name;
        }

        @Override
        public void setValue(PlaceholderData item, String value) {
            item.name = value != null ? value.trim() : "";
        }

        @Override
        public boolean isCellEditable(PlaceholderData item) {
            return true;
        }

        @Override
        public @Nullable TableCellRenderer getRenderer(PlaceholderData item) {
            return null;
        }

        @Override
        public @Nullable TableCellEditor getEditor(PlaceholderData item) {
            return null;
        }
    };

    private static final ColumnInfo<PlaceholderData, String> PATH_COLUMN = new ColumnInfo<PlaceholderData, String>("Path") {
        @Override
        public @Nullable String valueOf(PlaceholderData item) {
            return item.path;
        }

        @Override
        public void setValue(PlaceholderData item, String value) {
            item.path = value != null ? value.trim() : "";
        }

        @Override
        public boolean isCellEditable(PlaceholderData item) {
            return true;
        }

        @Override
        public @Nullable TableCellRenderer getRenderer(PlaceholderData item) {
            return null;
        }

        @Override
        public @Nullable TableCellEditor getEditor(PlaceholderData item) {
            return null;
        }
    };

    public PlaceholdersTableModel() {
        super(NAME_COLUMN, PATH_COLUMN);
    }

    public PlaceholdersTableModel(List<PlaceholderData> items) {
        super(NAME_COLUMN, PATH_COLUMN);
        setItems(items);
    }

    public void addPlaceholder() {
        addRow(new PlaceholderData("", ""));
    }

    public void removePlaceholder(int index) {
        if (index >= 0 && index < getRowCount()) {
            removeRow(index);
        }
    }

    public PlaceholderData getPlaceholder(int index) {
        if (index >= 0 && index < getRowCount()) {
            return getItem(index);
        }
        return null;
    }
}
