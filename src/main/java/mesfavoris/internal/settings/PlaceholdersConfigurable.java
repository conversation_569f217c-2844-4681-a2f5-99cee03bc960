package mesfavoris.internal.settings;

import com.intellij.openapi.options.Configurable;
import com.intellij.openapi.options.ConfigurationException;
import mesfavoris.internal.settings.PathPlaceholdersStore.PlaceholderData;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Placeholders configuration page in settings
 */
public class PlaceholdersConfigurable implements Configurable {
    
    private PlaceholdersPanel placeholdersPanel;
    private List<PlaceholderData> originalPlaceholders;

    @Nls(capitalization = Nls.Capitalization.Title)
    @Override
    public String getDisplayName() {
        return "Placeholders";
    }

    @Override
    public @Nullable JComponent createComponent() {
        try {
            if (placeholdersPanel == null) {
                placeholdersPanel = new PlaceholdersPanel();
                // Initialize with empty data first
                placeholdersPanel.setPlaceholders(new ArrayList<>());
                reset(); // Load actual data
            }
            return placeholdersPanel;
        } catch (Exception e) {
            // Fallback: create a simple error panel
            JPanel errorPanel = new JPanel(new BorderLayout());
            errorPanel.add(new JLabel("Error creating placeholders panel: " + e.getMessage()), BorderLayout.CENTER);
            e.printStackTrace(); // Log the error for debugging
            return errorPanel;
        }
    }

    @Override
    public boolean isModified() {
        if (placeholdersPanel == null || originalPlaceholders == null) {
            return false;
        }
        return placeholdersPanel.isModified(originalPlaceholders);
    }

    @Override
    public void apply() throws ConfigurationException {
        if (placeholdersPanel == null) {
            return;
        }

        List<PlaceholderData> placeholders = placeholdersPanel.getPlaceholders();
        
        // Data validation
        validatePlaceholders(placeholders);

        // Save
        PathPlaceholdersStore store = PathPlaceholdersStore.getInstance();
        store.setPlaceholders(placeholders);

        // Update original data
        originalPlaceholders = new ArrayList<>();
        for (PlaceholderData data : placeholders) {
            originalPlaceholders.add(new PlaceholderData(data.name, data.path));
        }
    }

    @Override
    public void reset() {
        if (placeholdersPanel == null) {
            return;
        }

        try {
            PathPlaceholdersStore store = PathPlaceholdersStore.getInstance();
            originalPlaceholders = store.getPlaceholders();

            // Create a copy to avoid direct modifications
            List<PlaceholderData> copy = new ArrayList<>();
            for (PlaceholderData data : originalPlaceholders) {
                copy.add(new PlaceholderData(data.name, data.path));
            }

            placeholdersPanel.setPlaceholders(copy);
        } catch (Exception e) {
            // Initialize with empty list if there's an error
            originalPlaceholders = new ArrayList<>();
            placeholdersPanel.setPlaceholders(new ArrayList<>());
        }
    }

    @Override
    public void disposeUIResources() {
        placeholdersPanel = null;
        originalPlaceholders = null;
    }

    private void validatePlaceholders(List<PlaceholderData> placeholders) throws ConfigurationException {
        Set<String> names = new HashSet<>();
        
        for (PlaceholderData data : placeholders) {
            // Ignore empty rows
            if ((data.name == null || data.name.trim().isEmpty()) &&
                (data.path == null || data.path.trim().isEmpty())) {
                continue;
            }

            // Check that name is not empty
            if (data.name == null || data.name.trim().isEmpty()) {
                throw new ConfigurationException("Placeholder name cannot be empty");
            }

            // Check that path is not empty
            if (data.path == null || data.path.trim().isEmpty()) {
                throw new ConfigurationException("Path for placeholder '" + data.name.trim() + "' cannot be empty");
            }
            
            // Check name uniqueness
            String trimmedName = data.name.trim();
            if (names.contains(trimmedName)) {
                throw new ConfigurationException("Placeholder name '" + trimmedName + "' is used multiple times");
            }
            names.add(trimmedName);

            // Check that name does not contain forbidden characters
            if (trimmedName.contains("${") || trimmedName.contains("}")) {
                throw new ConfigurationException("Placeholder name '" + trimmedName + "' cannot contain '${' or '}'");
            }
        }
    }
}
