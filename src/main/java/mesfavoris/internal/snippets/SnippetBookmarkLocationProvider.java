package mesfavoris.internal.snippets;

import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.project.Project;
import mesfavoris.bookmarktype.IBookmarkLocation;
import mesfavoris.bookmarktype.IBookmarkLocationProvider;
import mesfavoris.model.Bookmark;

public class SnippetB<PERSON>markLocationProvider implements IBookmarkLocationProvider {
    @Override
    public IBookmarkLocation getBookmarkLocation(Project project, Bookmark bookmark, ProgressIndicator progress) {
        String snippetContent = bookmark.getPropertyValue(SnippetBookmarkProperties.PROP_SNIPPET_CONTENT);
        if (snippetContent == null) {
            return null;
        }
        return new Snippet(snippetContent);
    }
}
