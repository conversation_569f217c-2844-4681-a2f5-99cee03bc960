<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<body>

    <p>Commons CLI -- version 1.3</p>

    <p>The commons-cli package aides in parsing command-line arguments.</p>

    <p>Allow command-line arguments to be parsed against a descriptor of
    valid options (long and short), potentially with arguments.</p>

    <p>command-line arguments may be of the typical <code>String[]</code>
    form, but also may be a <code>java.util.List</code>.  Indexes allow
    for parsing only a portion of the command-line.  Also, functionality
    for parsing the command-line in phases is built in, allowing for
    'cvs-style' command-lines, where some global options are specified
    before a 'command' argument, and command-specific options are
    specified after the command argument:

    <code>
    <pre>
        myApp -p &lt;port&gt; command -p &lt;printer&gt;
    </pre>
    </code>


    <p>The homepage for the project is
    <a href="http://commons.apache.org">Apache Commons/</a>
</body>
