            Apache Commons CLI
                Version 1.3.1
               Release Notes


INTRODUCTION:

This document contains the release notes for this version of the Commons CLI
package. Commons CLI provides a simple API for working with the command line
arguments and options.

Commons CLI 1.3.1 is a bug fix release and binary compatible with the
previous versions, except for the OptionValidator class that is no longer public
(change introduced in v1.2). Commons CLI 1.3.1 at least requires Java 5.0.

More information can be found on the project site at 
http://commons.apache.org/cli.

BUG FIXES
=========

o CLI-252: LongOpt falsely detected as ambiguous. Thanks to <PERSON>.


                Release Notes for version 1.3

NOTES
=====

A new parser is available: DefaultParser. It combines the features of the
GnuParser and the PosixParser. It also provides additional features like
partial matching for the long options, and long options without
separator (i.e like the JVM memory settings: -Xmx512m). This new parser
deprecates the previous ones.

DEPRECATIONS
============

o org.apache.commons.cli.BasicParser 
    replaced by org.apache.commons.cli.DefaultParser

o org.apache.commons.cli.GnuParser 
    replaced by org.apache.commons.cli.DefaultParser

o org.apache.commons.cli.OptionBuilder 
    replaced by org.apache.commons.cli.Option.builder()
                org.apache.commons.cli.Option.builder(String)
                org.apache.commons.cli.Option.Builder

o org.apache.commons.cli.Parser 
    replaced by org.apache.commons.cli.DefaultParser

o org.apache.commons.cli.PosixParser 
    replaced by org.apache.commons.cli.DefaultParser


NEW FEATURES
============

o CLI-161: PosixParser doesn't stop the parsing on "--" tokens following an
           option with an argument
o CLI-167: Support options like Java memory settings (-Xmx512M)
o CLI-181: Unified Parser
o CLI-224: Added new fluent API to create Option instances via builder class
           Option.Builder. This replaces the now deprecated OptionBuilder.
           Thanks to Duncan Jones, Brian Blount.
o CLI-160: PosixParser now supports partial long options (--ver instead of
           --version). 
o CLI-169: HelpFormatter now supports setting the displayed separator of long
           options. Thanks to J. Lewis Muir. 
o CLI-214: Added new method Options.addOption(String, String). Thanks to
           Alexandru Mocanu.


BUG FIXES
=========

o CLI-248: Dead links on doc page.
o CLI-234: Fixed code example in javadoc of
           "Option#Builder#valueSeparator(char)". Thanks to Greg Thomas.
o CLI-241: Clarified behavior of "OptionValidator#validateOption(String)"
           in case of null input. Thanks to Beluga Behr.
o CLI-202: Default options will now work correctly with required options that
           are missing.
o CLI-203: Default options will now work correctly together with option groups. 
o CLI-205: HelpFormatter.setArgName(String) now correctly sets the argument
           name.
o CLI-204: Passing default values for not defined options to a parser will now
           trigger a ParseException instead of a NullPointerException.
o CLI-201: Default properties provided as input to the Parser.parse() methods
           are now correctly processed.
o CLI-215: CommandLine.getParsedOptionValue() now returns a String object if no
           option type has been explicitly set. Thanks to Manuel M�ller. 
o CLI-212: HelpFormatter now prints command-line options in the same order as
           they have been added. Thanks to Per Cederberg.
o CLI-186: Standard help text now shows mandatory arguments also for the first
           option. Thanks to Kristoff Kiefer.
o CLI-207: HelpFormatter does not strip anymore leading whitespace in the
           footer text. Thanks to Uri Moszkowicz.
o CLI-185: Strip quotes contained in argument values only if there is exactly
           one at the beginning and one at the end. Thanks to
           Einar M. R. Rosenvinge.
o CLI-184: Negative numerical arguments take precedence over numerical options. 
o CLI-193: Fix possible StringIndexOutOfBoundsException in HelpFormatter.
           Thanks to Travis McLeskey.
o CLI-183: OptionGroups no longer throw an AlreadySelectedException when reused
           for several parsings.
o CLI-182: OptionGroup now selects properly an option with no short name.


CHANGES
=======

o CLI-240: Small cleanup of Option class. Thanks to Beluga Behr.
o CLI-230: Options.getRequiredOptions() now returns an unmodifiable list. 
o CLI-218: Clarify javadoc for CommandLine.getOptionValue() that the first
           specified argument will be returned. Thanks to Sven. 
o CLI-227: Changed unit tests to junit 4 annotation style. Thanks to
           Duncan Jones. 
o CLI-209: The javadoc of OptionBuilder now states that the class is not
           thread-safe. Thanks to Thomas Herre. 
o CLI-200: Fixed typo in javadoc of class CommandLine. Thanks to
           Gerard Weatherby. 
o CLI-223: Source code now uses generic types instead of raw types where
           possible. Thanks to Gerard Weatherby. 
o CLI-220  Corrected javadoc for return type of
           MissingOptionException.getMissingOptions(). Thanks to Joe Casadonte.
o CLI-197: Improve description of parameter "stopAtNonOption" in method
           CommandLine.parse(Options, String[], boolean). Thanks to
           Anders Larsson.
o CLI-231: Removed DoubleCheckedLocking test from checkstyle configuration.
           Thanks to Duncan Jones.


                Release Notes for version 1.2

NEW FEATURES
============

o --     : The method getOptionProperties() in the CommandLine class was added
           to retrieve easily the key/value pairs specified with options like
           -Dkey1=value1 -Dkey2=value2.
o CLI-157: GnuParser now supports long options with an '=' sign 
           (ie. --foo=bar and -foo=bar)
o CLI-155: The ordering of options can be defined in help messages.


BUG FIXES
=========

o CLI-137: The number of arguments defined for an option specifies the
           arguments per occurence of the option and not for all occurences.
o CLI-164: PosixParser no longer ignores unrecognized short options.
o CLI-163: PosixParser no longer stops the bursting process of a token if
           stopAtNonOption is enabled and a non option character is
           encountered.
o CLI-165: PosixParser no longer keeps processing the tokens after an 
           unrecognized long option when stopAtNonOption is enabled.
o CLI-156: Required options are properly checked if an Options instance is used
           twice to parse a command line.
o CLI-151: The line wrapping in HelpFormatter now works properly.


CHANGES
=======

o CLI-149: The message of MissingOptionException has been improved.
o CLI-86:  The exceptions have been enhanced with methods to retrieve easily
           the related options.
o CLI-141: Option.toString() now reports arguments properly.
o CLI-142: The Parser class has been changed to be more easily extendable.
o CLI-140: The following classes are now serializable: Option, OptionGroup,
           CommandLine and Options.
o --     : OptionValidator is no longer public, its methods were all private.


                Release Notes for version 1.1

NEW FEATURES
============

o CLI-78:  Setting description of a Option. 

CHANGES
=======

o CLI-2:   Wrong usage summary. 
o CLI-5:   Dependecy on commons-lang-2.0 but commons-lang-1.0 is obtained. 
o CLI-8:   Line separator as first char for helpformatter (footer) throws
           exception. 
o CLI-13:  CommandLine.getOptionValue() behaves contrary to docs. 
o CLI-21:  clone method in Option should use super.clone(). 
o CLI-23:  Passing properties in Parser does not work for options with a single
           argument. 
o CLI-26:  Only long options without short option seems to be noticed. 
o CLI-28:  Infinite Loop in Command-Line processing. 
o CLI-29:  Options should not be able to be added more than once. 
o CLI-35:  HelpFormatter doesn't sort options properly. 
o CLI-38:  HelpFormatter doesn't function correctly for options with only
           LongOpt. 
o CLI-44:  Document enhancement. 
o CLI-45:  Documentation errors. 
o CLI-51:  Parameter value "-something" misinterpreted as a parameter. 
o CLI-56:  clone() method doesn't fully clone contents. 
o CLI-59:  No Javadoc for HelpFormatter!. 
o CLI-65:  Parser breaks up command line parms into single characters. 
o CLI-67:  Missing arguments in HelpFormatter.renderOptions(..).
o CLI-69:  Error parsing option arguments.
o CLI-71:  A weakness of parser.
o CLI-129: CLI_1_BRANCH build.xml doesn't work.
o CLI-130: Remove the Commons Lang dependency.
o CLI-131: Options class returns options in random order.
o CLI-132: MissingOptionException should contain a useful error message.
o CLI-133: NullPointerException in Util.stripLeadingHyphens when passed a null
           argument. 
o CLI-134: 1.1 is not backwards compatible because it adds methods to the
           CommandLineParser interface. 
o CLI-135: Backwards compatibility between 1.1 and 1.0 broken due to
           Option.addValue removal. 


Historical list of changes: http://commons.apache.org/cli/changes-report.html

For complete information on Commons CLI, including instructions on how to
submit bug reports, patches, or suggestions for improvement, see the 
Apache Commons CLI website:

http://commons.apache.org/cli/

Have fun!
-Apache Commons CLI team
