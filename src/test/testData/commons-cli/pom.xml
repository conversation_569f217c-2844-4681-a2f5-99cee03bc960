<?xml version="1.0" encoding="UTF-8"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>38</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-cli</groupId>
  <artifactId>commons-cli</artifactId>
  <version>1.4-SNAPSHOT</version>
  <name>Apache Commons CLI</name>

  <inceptionYear>2002</inceptionYear>
  <description>
    Apache Commons CLI provides a simple API for presenting, processing and validating a command line interface.
  </description>

  <url>http://commons.apache.org/proper/commons-cli/</url>

  <issueManagement>
    <system>jira</system>
    <url>http://issues.apache.org/jira/browse/CLI</url>
  </issueManagement>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/cli/trunk/</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/cli/trunk/</developerConnection>
    <url>http://svn.apache.org/viewvc/commons/proper/cli/trunk/</url>
  </scm>

  <developers>
    <developer>
      <name>James Strachan</name>
      <id>jstrachan</id>
      <email><EMAIL></email>
      <organization>SpiritSoft, Inc.</organization>
    </developer>
    <developer>
      <name>Bob McWhirter</name>
      <id>bob</id>
      <email><EMAIL></email>
      <organization>Werken</organization>
      <roles>
        <role>contributed ideas and code from werken.opt</role>
      </roles>
    </developer>
    <developer>
      <name>John Keyes</name>
      <id>jkeyes</id>
      <email><EMAIL></email>
      <organization>integral Source</organization>
      <roles>
        <role>contributed ideas and code from Optz</role>
      </roles>
    </developer>
    <developer>
      <name>Rob Oxspring</name>
      <id>roxspring</id>
      <email><EMAIL></email>
      <organization>Indigo Stone</organization>
      <roles>
        <role>designed CLI2</role>
      </roles>
    </developer>
    <developer>
      <name>Emmanuel Bourg</name>
      <id>ebourg</id>
      <email><EMAIL></email>
      <organization>Ariane Software</organization>
    </developer>
    <developer>
      <name>Thomas Neidhart</name>
      <id>tn</id>
      <email><EMAIL></email>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Beluga Behr</name>
    </contributor>
    <contributor>
      <name>Peter Donald</name>
      <roles>
        <role>contributed ideas and code from Avalon Excalibur's cli package</role>
      </roles>
    </contributor>
    <contributor>
      <name>Brian Egge</name>
      <roles>
        <role>made the 1.1 release happen</role>
      </roles>
    </contributor>
    <contributor>
      <name>Duncan Jones</name>
      <roles>
        <role>supplied patches</role>
      </roles>
    </contributor>
    <contributor>
      <name>Berin Loritsch</name>
      <email><EMAIL></email>
      <roles>
        <role>helped in the Avalon CLI merge</role>
      </roles>
    </contributor>
    <contributor>
      <name>Peter Maddocks</name>
      <email><EMAIL></email>
      <organization>Hewlett-Packard</organization>
      <roles>
        <role>supplied patch</role>
      </roles>
    </contributor>
    <contributor>
      <name>Alexandru Mocanu</name>
      <roles>
        <role>supplied patch</role>
      </roles>
    </contributor>
    <contributor>
      <name>Andrew Shirley</name>
      <roles>
        <role>lots of fixes for 1.1</role>
      </roles>
    </contributor>
    <contributor>
      <name>Greg Thomas</name>
    </contributor>
    <contributor>
      <name>Slawek Zachcial</name>
      <roles>
        <role>unit tests</role>
      </roles>
    </contributor>
    
  </contributors>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <properties>
    <maven.compiler.source>1.5</maven.compiler.source>
    <maven.compiler.target>1.5</maven.compiler.target>
    <commons.componentid>cli</commons.componentid>
    <commons.release.version>1.3.1</commons.release.version>
    <commons.release.name>commons-cli-${commons.release.version}</commons.release.name>
    <commons.osgi.symbolicName>org.apache.commons.cli</commons.osgi.symbolicName>
    <commons.jira.id>CLI</commons.jira.id>
    <commons.jira.pid>12310463</commons.jira.pid>
    <!-- The RC version used in the staging repository URL. -->
    <commons.rc.version>RC1</commons.rc.version>

    <commons.scmPubCheckoutDirectory>site-content</commons.scmPubCheckoutDirectory>
  </properties>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/bin.xml</descriptor>
            <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
    </plugins>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <configuration>
            <ignorePathsToDelete>
              <ignorePathToDelete>javadocs**</ignorePathToDelete>
            </ignorePathsToDelete>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <linksource>true</linksource>
          <links>
            <link>http://download.oracle.com/javase/6/docs/api</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.15</version>
        <configuration>
          <configLocation>${basedir}/src/conf/checkstyle.xml</configLocation>
          <enableRulesSummary>false</enableRulesSummary>
          <suppressionsLocation>${basedir}/src/conf/checkstyle-suppressions.xml</suppressionsLocation>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>checkstyle</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>3.0.1</version>
        <configuration>
          <threshold>Normal</threshold>
          <effort>Default</effort>
          <excludeFilterFile>${basedir}/src/conf/findbugs-exclude-filter.xml</excludeFilterFile>
       </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.5</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Commons Site</name>
      <url>scm:svn:${commons.scmPubUrl}</url>
    </site>
  </distributionManagement>

  <profiles>
    <profile>
      <id>rc</id>
      <distributionManagement>
        <!-- Cannot define in parent ATM, see COMMONSSITE-26 -->
        <site>
          <id>apache.website</id>
          <name>Apache Commons Release Candidate Staging Site</name>
          <url>${commons.deployment.protocol}://people.apache.org/www/people.apache.org/builds/commons/${commons.componentid}/${commons.release.version}/${commons.rc.version}/site</url>
        </site>
      </distributionManagement>
    </profile>
    <profile>
      <id>setup-checkout</id>
      <activation>
        <file>
          <missing>site-content</missing>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <version>1.7</version>
            <executions>
              <execution>
                <id>prepare-checkout</id>
                <phase>pre-site</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <tasks>
                    <exec executable="svn">
                      <arg line="checkout --depth immediates ${commons.scmPubUrl} ${commons.scmPubCheckoutDirectory}" />
                    </exec>

                    <exec executable="svn">
                      <arg line="update --set-depth exclude ${commons.scmPubCheckoutDirectory}/javadocs" />
                    </exec>

                    <pathconvert pathsep=" " property="dirs">
                      <dirset dir="${commons.scmPubCheckoutDirectory}" includes="*" />
                    </pathconvert>
                    <exec executable="svn">
                      <arg line="update --set-depth infinity ${dirs}" />
                    </exec>
                  </tasks>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
