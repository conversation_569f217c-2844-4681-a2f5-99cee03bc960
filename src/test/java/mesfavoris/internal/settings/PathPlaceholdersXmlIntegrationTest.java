package mesfavoris.internal.settings;

import mesfavoris.internal.placeholders.PathPlaceholderResolver;
import mesfavoris.internal.settings.PathPlaceholdersStore.PlaceholderData;
import mesfavoris.placeholders.IPathPlaceholderResolver;
import mesfavoris.placeholders.PathPlaceholder;
import org.jdom.Element;
import org.junit.Test;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * Integration test for XML serialization with placeholder resolution
 */
public class PathPlaceholdersXmlIntegrationTest {

    @Test
    public void testXmlSerializationWithPlaceholderResolution() {
        // Given - create store with placeholders
        PathPlaceholdersStore store = new PathPlaceholdersStore();
        List<PlaceholderData> placeholders = Arrays.asList(
            new PlaceholderData("HOME", "/home/<USER>"),
            new PlaceholderData("WORK", "/work/projects"),
            new PlaceholderData("TEMP", "/tmp")
        );
        store.setPlaceholders(placeholders);

        // When - serialize to XML
        Element xmlState = store.getState();

        // Create new store and deserialize
        PathPlaceholdersStore newStore = new PathPlaceholdersStore();
        newStore.loadState(xmlState);

        // Then - verify placeholder resolution works
        IPathPlaceholderResolver resolver = new PathPlaceholderResolver(newStore);
        
        // Test expansion
        Path expandedPath = resolver.expand("${HOME}/documents");
        assertNotNull("Expanded path should not be null", expandedPath);
        assertEquals("Should expand HOME placeholder", 
            Paths.get("/home/<USER>/documents").toAbsolutePath(), expandedPath);

        Path workPath = resolver.expand("${WORK}/myproject");
        assertNotNull("Work path should not be null", workPath);
        assertEquals("Should expand WORK placeholder",
            Paths.get("/work/projects/myproject").toAbsolutePath(), workPath);

        // Test collapse
        String collapsedPath = resolver.collapse(Paths.get("/home/<USER>/documents/file.txt"));
        assertEquals("Should collapse to HOME placeholder", "${HOME}/documents/file.txt", collapsedPath);
    }

    @Test
    public void testXmlSerializationPreservesOrder() {
        // Given
        PathPlaceholdersStore store = new PathPlaceholdersStore();
        List<PlaceholderData> originalOrder = Arrays.asList(
            new PlaceholderData("ALPHA", "/alpha"),
            new PlaceholderData("BETA", "/beta"),
            new PlaceholderData("GAMMA", "/gamma")
        );
        store.setPlaceholders(originalOrder);

        // When - serialize and deserialize
        Element xmlState = store.getState();
        PathPlaceholdersStore newStore = new PathPlaceholdersStore();
        newStore.loadState(xmlState);

        // Then - verify order is preserved
        List<PlaceholderData> restoredOrder = newStore.getPlaceholders();
        assertEquals("Should have same number of placeholders", 3, restoredOrder.size());
        assertEquals("First placeholder name", "ALPHA", restoredOrder.get(0).name);
        assertEquals("Second placeholder name", "BETA", restoredOrder.get(1).name);
        assertEquals("Third placeholder name", "GAMMA", restoredOrder.get(2).name);
    }

    @Test
    public void testXmlSerializationWithSpecialCharacters() {
        // Given - placeholders with special characters
        PathPlaceholdersStore store = new PathPlaceholdersStore();
        List<PlaceholderData> placeholders = Arrays.asList(
            new PlaceholderData("PATH_WITH_SPACES", "/path with spaces"),
            new PlaceholderData("PATH_WITH_UNICODE", "/path/with/üñíçødé"),
            new PlaceholderData("PATH_WITH_QUOTES", "/path/with/\"quotes\"")
        );
        store.setPlaceholders(placeholders);

        // When - serialize and deserialize
        Element xmlState = store.getState();
        PathPlaceholdersStore newStore = new PathPlaceholdersStore();
        newStore.loadState(xmlState);

        // Then - verify special characters are preserved
        List<PlaceholderData> restored = newStore.getPlaceholders();
        assertEquals("Should preserve spaces", "/path with spaces", restored.get(0).path);
        assertEquals("Should preserve unicode", "/path/with/üñíçødé", restored.get(1).path);
        assertEquals("Should preserve quotes", "/path/with/\"quotes\"", restored.get(2).path);
    }

    @Test
    public void testXmlSerializationWithEmptyValues() {
        // Given - placeholders with empty values (should be filtered out)
        PathPlaceholdersStore store = new PathPlaceholdersStore();
        List<PlaceholderData> placeholders = Arrays.asList(
            new PlaceholderData("VALID", "/valid/path"),
            new PlaceholderData("", "/empty/name"),
            new PlaceholderData("EMPTY_PATH", ""),
            new PlaceholderData("", "")
        );
        store.setPlaceholders(placeholders);

        // When - serialize and deserialize
        Element xmlState = store.getState();
        PathPlaceholdersStore newStore = new PathPlaceholdersStore();
        newStore.loadState(xmlState);

        // Then - verify all placeholders are preserved in XML (even invalid ones)
        // but only valid ones work for resolution
        List<PlaceholderData> restored = newStore.getPlaceholders();
        assertEquals("Should preserve all placeholders in XML", 4, restored.size());

        // But only valid placeholders should work for resolution
        List<PathPlaceholder> validPlaceholders = newStore.getPathPlaceholders();
        assertEquals("Should have only 1 valid placeholder", 1, validPlaceholders.size());
        assertEquals("Valid placeholder name", "VALID", validPlaceholders.get(0).getName());
    }
}
