package mesfavoris.internal.settings;

import mesfavoris.internal.settings.PathPlaceholdersStore.PlaceholderData;
import mesfavoris.placeholders.IPathPlaceholders;
import mesfavoris.placeholders.PathPlaceholder;
import org.junit.Test;

import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import static org.junit.Assert.*;

/**
 * Test for PathPlaceholdersStore
 */
public class PathPlaceholdersStoreTest {

    @Test
    public void testPlaceholderDataToPathPlaceholder() {
        // Given
        PlaceholderData data = new PlaceholderData("HOME", "/home/<USER>");

        // When
        PathPlaceholder placeholder = data.toPathPlaceholder();

        // Then
        assertNotNull(placeholder);
        assertEquals("HOME", placeholder.getName());
        assertEquals(Paths.get("/home/<USER>").toAbsolutePath(), placeholder.getPath());
    }

    @Test
    public void testPlaceholderDataToPathPlaceholderWithEmptyName() {
        // Given
        PlaceholderData data = new PlaceholderData("", "/home/<USER>");

        // When
        PathPlaceholder placeholder = data.toPathPlaceholder();

        // Then
        assertNull(placeholder);
    }

    @Test
    public void testPlaceholderDataToPathPlaceholderWithEmptyPath() {
        // Given
        PlaceholderData data = new PlaceholderData("HOME", "");

        // When
        PathPlaceholder placeholder = data.toPathPlaceholder();

        // Then
        assertNull(placeholder);
    }

    @Test
    public void testFromPathPlaceholder() {
        // Given
        PathPlaceholder placeholder = new PathPlaceholder("HOME", Paths.get("/home/<USER>"));

        // When
        PlaceholderData data = PlaceholderData.fromPathPlaceholder(placeholder);

        // Then
        assertEquals("HOME", data.name);
        assertEquals(placeholder.getPath().toString(), data.path);
    }

    @Test
    public void testGetPathPlaceholders() {
        // Given
        PathPlaceholdersStore store = new PathPlaceholdersStore();
        List<PlaceholderData> placeholderDataList = Arrays.asList(
            new PlaceholderData("HOME", "/home/<USER>"),
            new PlaceholderData("WORK", "/work/projects"),
            new PlaceholderData("", ""), // Should be filtered out
            new PlaceholderData("INVALID", "") // Should be filtered out
        );
        store.setPlaceholders(placeholderDataList);

        // When
        List<PathPlaceholder> pathPlaceholders = store.getPathPlaceholders();

        // Then
        assertEquals(2, pathPlaceholders.size());
        assertEquals("HOME", pathPlaceholders.get(0).getName());
        assertEquals("WORK", pathPlaceholders.get(1).getName());
    }

    @Test
    public void testIPathPlaceholdersInterface() {
        // Given
        PathPlaceholdersStore store = new PathPlaceholdersStore();
        List<PlaceholderData> placeholderDataList = Arrays.asList(
            new PlaceholderData("HOME", "/home/<USER>"),
            new PlaceholderData("WORK", "/work/projects")
        );
        store.setPlaceholders(placeholderDataList);

        // When - use as IPathPlaceholders
        IPathPlaceholders placeholders = store;

        // Then - test get method
        PathPlaceholder homePlaceholder = placeholders.get("HOME");
        assertNotNull("HOME placeholder should exist", homePlaceholder);
        assertEquals("HOME", homePlaceholder.getName());
        assertEquals(Paths.get("/home/<USER>").toAbsolutePath(), homePlaceholder.getPath());

        PathPlaceholder workPlaceholder = placeholders.get("WORK");
        assertNotNull("WORK placeholder should exist", workPlaceholder);
        assertEquals("WORK", workPlaceholder.getName());

        PathPlaceholder nonExistent = placeholders.get("NONEXISTENT");
        assertNull("Non-existent placeholder should return null", nonExistent);

        // Test iterator
        Iterator<PathPlaceholder> iterator = placeholders.iterator();
        assertTrue("Iterator should have elements", iterator.hasNext());

        int count = 0;
        for (PathPlaceholder placeholder : placeholders) {
            assertNotNull("Placeholder should not be null", placeholder);
            count++;
        }
        assertEquals("Should iterate over 2 placeholders", 2, count);
    }
}
